<?php

namespace App\Services;

use App\Enums\ServiceProviderStatusEnum;
use App\Enums\ServiceProviderTypeEnum;
use App\Enums\UserStatusEnum;
use App\Enums\UserTypeEnum;
use App\Jobs\SendServiceProviderUpdateNotificationJob;
use App\Models\City;
use App\Models\Notification;
use App\Models\Region;
use App\Models\ServiceLevel;
use App\Models\ServiceProvider;
use App\Models\Supervisor;
use App\Models\User;
use App\Notifications\ServiceProvider\ServiceProviderApprovalNotification;
use App\Notifications\ServiceProvider\ServiceProviderRegisteredNotification;
use App\Notifications\ServiceProvider\ServiceProviderStatusNotification;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use App\Jobs\ProcessServiceProviderMedia;
use App\Jobs\ProcessServiceProviderText;
use App\Jobs\SendServiceProviderNotifications;
use Illuminate\Support\Facades\Log;
use Throwable;

class ServiceProviderService extends BaseService
{
    public function __construct(protected ServiceProvider $serviceProvider)
    {
        parent::__construct($serviceProvider);
    }

    /**
     * Store or update a ServiceProvider.
     */
    public function store($data): ServiceProvider
    {
        DB::beginTransaction();

        // Ensure phone number is properly formatted
        $serviceProviderData = $data;

        $serviceProvider = ServiceProvider::updateOrCreate(['id' => $data['id'] ?? null], $serviceProviderData);
        $serviceProviderUser = User::where('id', $data['user_id'])->update([
            'type' => UserTypeEnum::SERVICE_PROVIDER->value,
            'status' => UserStatusEnum::ACTIVE->value,
        ]);
        $this->handleImages($serviceProvider, $data['images'] ?? []);

        if (isset($data['file'])) {
            upload_media_file($serviceProvider, $data['file'], 'file', $serviceProvider->getDisk());
        }
        if (isset($data['id_image_front'])) {
            upload_media_file($serviceProvider, $data['id_image_front'], 'id_image_front', $serviceProvider->getDisk(), true);
        }
        if (isset($data['id_image_back'])) {
            upload_media_file($serviceProvider, $data['id_image_back'], 'id_image_back', $serviceProvider->getDisk(), true);
        }
        if (isset($data['face_image'])) {
            upload_media_file($serviceProvider, $data['face_image'], 'face_image', $serviceProvider->getDisk(), true);
        }
        // Attach service categories
        if (isset($data['service_category_ids'])) {
            $serviceProvider->serviceCategories()->sync($data['service_category_ids']);
        }
        $this->handleMainImage($serviceProvider, $data['main_image'] ?? null);
        $this->syncRegions($serviceProvider, $data['region_id'] ?? []);

        DB::commit();

        return $serviceProvider;
    }

    public function update(Model|ServiceProvider $serviceProvider, $data): Model
    {
        $this->handleImages($serviceProvider, $data['images'] ?? []);
        if (isset($data['file'])) {
            upload_media_file($serviceProvider, $data['file'], 'file', $serviceProvider->getDisk(), true);
        }
        $this->handleMainImage($serviceProvider, $data['main_image'] ?? null);
        if (isset($data['user_id'])) {
            $serviceProviderUser = User::where('id', $data['user_id'])->update([
                'type' => UserTypeEnum::SERVICE_PROVIDER->value,
            ]);
        }

        // Attach service categories
        if (isset($data['service_category_ids'])) {
            $serviceProvider->serviceCategories()->sync($data['service_category_ids']);
        }
        $this->syncRegions($serviceProvider, $data['region_id'] ?? []);
        parent::update($serviceProvider, $data);

        return $serviceProvider;
    }

    /**
     * Get cities and regions based on the user's role.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getCitiesAndRegionsForUser(User $user)
    {
        if ($user->type === UserTypeEnum::ADMIN) {
            return City::whereHas('regions')->with('regions')->get();
        }
        if ($user->type === UserTypeEnum::SUPERVISOR) {
            // Get the city and region IDs associated with the supervisor
            $supervisor = $user->supervisor;

            if ($supervisor) {
                $cityIds = $supervisor->cityables->pluck('city_id')->toArray();
                $regionIds = $supervisor->cityables->pluck('region_id')->toArray();

                // Get the cities and their regions associated with these IDs
                return City::whereHas('regions', function ($query) use ($cityIds, $regionIds) {
                    $query->whereIn('city_id', $cityIds)
                        ->orWhereIn('id', $regionIds);
                })->with(['regions' => function ($query) use ($cityIds, $regionIds) {
                    $query->whereIn('city_id', $cityIds)
                        ->orWhereIn('id', $regionIds);
                }])->get();
            }
        }

        // Admins or other users with no associations get all cities and regions
        return collect(); // Return an empty collection for other user types
    }

    /**
     * Get users excluding admin and supervisor.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getFilteredUsers($serviceProvider = null)
    {
        $query = User::where('status', UserStatusEnum::ACTIVE->value)
                     ->where(function($q) {
                         $q->where('type', UserTypeEnum::USER->value)
                           ->orWhere('type', UserTypeEnum::SERVICE_PROVIDER->value);
                     });

        // If we're editing an existing service provider, include its user
        if ($serviceProvider && $serviceProvider->user_id) {
            $query->orWhere('id', $serviceProvider->user_id);
        }

        return $query->get();
    }

    /**
     * Handle image uploads for the ServiceProvider.
     */
    public function handleImages(ServiceProvider $serviceProvider, array $images): void
    {
        foreach ($images as $image) {
        upload_media_file($serviceProvider,$image, 'image', $serviceProvider->getDisk());

        }
    }

    /**
     * Handle main image upload for the ServiceProvider.
     */
    public function handleMainImage(ServiceProvider $serviceProvider, ?string $mainImage): void
    {
        if ($mainImage) {
            upload_media_file($serviceProvider, $mainImage, 'main_image', $serviceProvider->getDisk(), true);
        }
    }

    /**
     * Sync the regions associated with the ServiceProvider.
     */
    public function syncRegions(ServiceProvider $serviceProvider, array $regionIds = [], array $cityIds = []): void
    {
        // If city IDs are provided, fetch all regions related to these cities
        if (!empty($cityIds) && empty($regionIds)) {
            $additionalRegionIds = Region::whereIn('city_id', $cityIds)->pluck('id')->toArray();
            $regionIds = array_merge($regionIds, $additionalRegionIds);
        }

        // Ensure unique region IDs
        $regionIds = array_unique($regionIds);
        // Sync the regions with the service provider
        $serviceProvider->cityables()->sync($regionIds);
    }

    /**
     * Upload a media file for the ServiceProvider.
     */
    public function uploadMediaFile(ServiceProvider $serviceProvider, string $file, string $collection): void
    {
        upload_media_file($serviceProvider, $file, $collection, $serviceProvider->getDisk());
    }

    /**
     * Register a new ServiceProvider.
     */
    // public function registerServiceProvider(array $data)
    // {
    //     DB::beginTransaction();
    //     try {
    //         $data['user_id'] = auth()->user()->id;

    //         $data['status'] = ServiceProviderStatusEnum::PENDING->value;

    //         $serviceProvider = parent::store($data);

    //         // Process text content for inappropriate content
    //         $textData = [
    //             'company_name' => $serviceProvider->company_name ?? '',
    //             'description' => $serviceProvider->description ?? '',
    //         ];

    //         // Add translations if available
    //         if ($serviceProvider->translations) {
    //             foreach ($serviceProvider->translations as $translation) {
    //                 $locale = $translation->locale;
    //                 $textData["company_name_{$locale}"] = $translation->company_name ?? '';
    //                 $textData["description_{$locale}"] = $translation->description ?? '';
    //             }
    //         }

    //         $hasImages = false;

    //         $mediaData = [
    //             'main_image' => null,
    //             'face_image' => null,
    //             'id_image_front' => null,
    //             'id_image_back' => null,
    //             'file' => null,
    //             'images' => []
    //         ];

    //         if (isset($data['main_image']) && $data['main_image'] && $data['main_image']->isValid()) {
    //             $hasImages = true;
    //             $path = $data['main_image']->store('temp/service-providers', 'public');
    //             $mediaData['main_image'] = storage_path('app/public/' . $path);
    //         }

    //         if (isset($data['face_image']) && $data['face_image'] && $data['face_image']->isValid()) {
    //             $hasImages = true;
    //             $path = $data['face_image']->store('temp/service-providers', 'public');
    //             $mediaData['face_image'] = storage_path('app/public/' . $path);
    //         }

    //         if (isset($data['id_image_front']) && $data['id_image_front'] && $data['id_image_front']->isValid()) {
    //             $hasImages = true;
    //             $path = $data['id_image_front']->store('temp/service-providers', 'public');
    //             $mediaData['id_image_front'] = storage_path('app/public/' . $path);
    //         }

    //         if (isset($data['id_image_back']) && $data['id_image_back'] && $data['id_image_back']->isValid()) {
    //             $hasImages = true;
    //             $path = $data['id_image_back']->store('temp/service-providers', 'public');
    //             $mediaData['id_image_back'] = storage_path('app/public/' . $path);
    //         }

    //         if (!empty($data['images'])) {
    //             foreach ($data['images'] as $image) {
    //                 if ($image && $image->isValid()) {
    //                     $hasImages = true;
    //                     $path = $image->store('temp/service-providers', 'public');
    //                     $mediaData['images'][] = storage_path('app/public/' . $path);
    //                 }
    //             }
    //         }

    //         if (isset($data['file']) && $data['file'] && $data['file']->isValid()) {
    //             $hasImages = true;
    //             $path = $data['file']->store('temp/service-providers', 'public');
    //             $mediaData['file'] = storage_path('app/public/' . $path);
    //         }

    //         $this->syncCategories($serviceProvider, $data['service_category_id'] ?? []);
    //         $this->syncRegions($serviceProvider, $data['region_id'] ?? [], $data['city_id'] ?? []);

    //         if ($hasImages) {
    //             $serviceProvider->update([
    //                 'status' => ServiceProviderStatusEnum::PENDING->value
    //             ]);

    //             ProcessServiceProviderMedia::dispatch(
    //                 $serviceProvider->id,
    //                 $mediaData
    //             );
    //         } else {
    //             $serviceProvider->update([
    //                 'status' => ServiceProviderStatusEnum::APPROVED->value
    //             ]);

    //             $serviceProvider->user->notify(new ServiceProviderApprovalNotification($serviceProvider));
    //         }


    //          // Dispatch job to check text content
    //         ProcessServiceProviderText::dispatch(
    //             $serviceProvider->id,
    //             $textData
    //         );
            
    //         DB::commit();

    //         return $serviceProvider;
    //     } catch (Throwable $e) {
    //         DB::rollBack();
    //         Log::error('Failed to register service provider', [
    //             'error' => $e->getMessage(),
    //             'trace' => $e->getTraceAsString()
    //         ]);
    //         throw $e;
    //     }
    // }
    
     public function registerServiceProvider(array $data)
    {
        DB::beginTransaction();
        try {
            $data['user_id'] = auth()->user()->id;

            $data['status'] = ServiceProviderStatusEnum::PENDING->value;

            $serviceProvider = parent::store($data);

            // Process text content for inappropriate content
            $textData = [
                'company_name' => $serviceProvider->company_name ?? '',
                'description' => $serviceProvider->description ?? '',
            ];

            // Add translations if available
            if ($serviceProvider->translations) {
                foreach ($serviceProvider->translations as $translation) {
                    $locale = $translation->locale;
                    $textData["company_name_{$locale}"] = $translation->company_name ?? '';
                    $textData["description_{$locale}"] = $translation->description ?? '';
                }
            }

            $hasImages = false;

            $mediaData = [
                'main_image' => null,
                'face_image' => null,
                'id_image_front' => null,
                'id_image_back' => null,
                'file' => null,
                'images' => []
            ];

            $this->handleMainImage($serviceProvider, $data['main_image'] ?? null);

            if (isset($data['id_image_back'])) {
                upload_media_file($serviceProvider, $data['id_image_back'], 'id_image_back', $serviceProvider->getDisk(), true);
            }
            if (isset($data['face_image'])) {
                upload_media_file($serviceProvider, $data['face_image'], 'face_image', $serviceProvider->getDisk(), true);
            }
            if (isset($data['id_image_front'])) {
                upload_media_file($serviceProvider, $data['id_image_front'], 'id_image_front', $serviceProvider->getDisk(), true);
            }
            if (isset($data['id_image_back'])) {
                upload_media_file($serviceProvider, $data['id_image_back'], 'id_image_back', $serviceProvider->getDisk(), true);
            }

            if (!empty($data['images'])) {
                foreach ($data['images'] as $image) {
                    if ($image && $image->isValid()) {
                        $hasImages = true;
                        $path = $image->store('temp/service-providers', 'public');
                        $mediaData['images'][] = storage_path('app/public/' . $path);
                    }
                }
            }

            if (isset($data['file']) && $data['file'] && $data['file']->isValid()) {
                $hasImages = true;
                $path = $data['file']->store('temp/service-providers', 'public');
                $mediaData['file'] = storage_path('app/public/' . $path);
            }

            $this->syncCategories($serviceProvider, $data['service_category_id'] ?? []);
            $this->syncRegions($serviceProvider, $data['region_id'] ?? [], $data['city_id'] ?? []);

            
                $serviceProvider->update([
                    'status' => ServiceProviderStatusEnum::APPROVED->value
                ]);

                $serviceProvider->user->notify(new ServiceProviderApprovalNotification($serviceProvider));
            DB::commit();

            return $serviceProvider;
        } catch (Throwable $e) {
            DB::rollBack();
            Log::error('Failed to register service provider', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    public function getProfile(User $user): Model|Exception
    {
        return $user->serviceProvider()
            ->with(['cityables', 'serviceCategories', 'media'])
            ->firstOrFail();
    }

    /**
     * Sync the regions associated with the ServiceProvider.
     */
    public function syncCategories(ServiceProvider $serviceProvider, array $categoriesIds = []): void
    {
        $serviceProvider->serviceCategories()->sync($categoriesIds);
    }

    public function updateServiceProvider(ServiceProvider $serviceProvider, array $data): Model
    {
        parent::update($serviceProvider, $data);

        if (isset($data['deleted_images']) && !empty($data['deleted_images'])) {
            $this->deleteImages($serviceProvider, $data['deleted_images']);
        }

        // Track if any images were uploaded
        $hasImages = false;

        // Prepare media data for processing
        $mediaData = [
            'main_image' => null,
            'face_image' => null,
            'id_image_front' => null,
            'id_image_back' => null,
            'file' => null,
            'images' => []
        ];

        // Handle regular images
        if (!empty($data['images'])) {
            foreach ($data['images'] as $image) {
                if ($image && $image->isValid()) {
                    $hasImages = true;
                    $path = $image->store('temp/service-providers', 'public');
                    $mediaData['images'][] = storage_path('app/public/' . $path);
                }
            }
        }

        // Handle main image
        if (isset($data['main_image']) && $data['main_image'] && $data['main_image']->isValid()) {
            $hasImages = true;
            $path = $data['main_image']->store('temp/service-providers', 'public');
            $mediaData['main_image'] = storage_path('app/public/' . $path);
        }

        // Handle ID image front
        if (isset($data['id_image_front']) && $data['id_image_front'] && $data['id_image_front']->isValid()) {
            $hasImages = true;
            $path = $data['id_image_front']->store('temp/service-providers', 'public');
            $mediaData['id_image_front'] = storage_path('app/public/' . $path);
        }

        // Handle ID image back
        if (isset($data['id_image_back']) && $data['id_image_back'] && $data['id_image_back']->isValid()) {
            $hasImages = true;
            $path = $data['id_image_back']->store('temp/service-providers', 'public');
            $mediaData['id_image_back'] = storage_path('app/public/' . $path);
        }

        // Handle face image
        if (isset($data['face_image']) && $data['face_image'] && $data['face_image']->isValid()) {
            $hasImages = true;
            $path = $data['face_image']->store('temp/service-providers', 'public');
            $mediaData['face_image'] = storage_path('app/public/' . $path);
        }

        // Handle file (no content check needed for files)
        if (isset($data['file']) && $data['file'] && $data['file']->isValid()) {
            $hasImages = true;
            $path = $data['file']->store('temp/service-providers', 'public');
            $mediaData['file'] = storage_path('app/public/' . $path);
        }

        $this->syncCategories($serviceProvider, $data['service_category_id'] ?? []);
        $this->syncRegions($serviceProvider, $data['region_id'] ?? [], $data['city_id'] ?? []);

        // Process text content for inappropriate content
        $textData = [
            'company_name' => $serviceProvider->company_name ?? '',
            'description' => $serviceProvider->description ?? '',
        ];

        // Add translations if available
        if ($serviceProvider->translations) {
            foreach ($serviceProvider->translations as $translation) {
                $locale = $translation->locale;
                $textData["company_name_{$locale}"] = $translation->company_name ?? '';
                $textData["description_{$locale}"] = $translation->description ?? '';
            }
        }

        // Make sure the status is set to PENDING while content is being processed
        $serviceProvider->update([
            'status' => ServiceProviderStatusEnum::PENDING->value,
            'reason' => null // Clear any previous rejection reason
        ]);

        // Dispatch job to check text content
        ProcessServiceProviderText::dispatch(
            $serviceProvider->id,
            $textData
        );

        // Dispatch a single job to process all media
        if ($hasImages) {
            // Dispatch the job to process all media
            ProcessServiceProviderMedia::dispatch(
                $serviceProvider->id,
                $mediaData
            );
        }

        return $serviceProvider->fresh();
    }

    public function deleteImages(ServiceProvider $serviceProvider, array $deletedImageUrls): void
{
    foreach ($deletedImageUrls as $imageUrl) {
        $imageName = pathinfo($imageUrl, PATHINFO_FILENAME);
        $mediaItem = $serviceProvider->media()->firstWhere('name', $imageName);

        if ($mediaItem) {
            $mediaItem->delete();
        }
    }
}


    protected function sendNotificationToRelevantUsers(ServiceProvider $serviceProvider)
    {
        try{
            $admins = User::where('type', UserTypeEnum::ADMIN->value)->get();

            foreach ($admins as $admin) {
                $admin->notify(new ServiceProviderRegisteredNotification($serviceProvider));
            }
        }catch(Exception $e){

            info('sendNotificationToRelevantUsers');
            info($e->getMessage());
        }

    }

    /**
     * Update the service provider status and reason, then send a notification.
     *
     * @param int $id
     * @param string $status
     * @param string|null $reason
     * @return void
     */
    public function updateServiceProviderStatus(int $id, string $status, ?string $reason = null): void
    {
        $serviceProvider = $this->show($id);

        $serviceProvider->update([
            'status' => $status,
            'reason' => $reason,
        ]);

        $this->sendNotificationToRelevantProviders($serviceProvider, $status);

    }

    /**
     * Send notification to the service provider about status update.
     *
     * @param ServiceProvider $serviceProvider
     * @param string $status
     * @return void
     */
    public function sendNotificationToRelevantProviders(ServiceProvider $serviceProvider, string $status): void
    {
        $serviceProvider->user->notify(new ServiceProviderStatusNotification($serviceProvider, $status));
    }

    public function updateServiceProviderLevel(ServiceProvider $provider)
    {
        $totalServices = $provider->orders()->count();
        $averageRating = $provider->reviews()->avg('rating');

        $serviceLevel = ServiceLevel::where('required_services_count', '<=', $totalServices)
            ->where('min_rating', '<=', $averageRating)
            ->where(function ($query) use ($averageRating) {
                $query->whereNull('max_rating')
                    ->orWhere('max_rating', '>=', $averageRating);
            })
            ->orderBy('required_services_count', 'desc')
            ->first();

        $provider->update(['service_level_id' => $serviceLevel->id ?? null]);
    }

    /**
     * Handle face image upload for the ServiceProvider.
     */
    public function handleFaceImage(ServiceProvider $serviceProvider, ?string $faceImage): void
    {
        if ($faceImage) {
            upload_media_file($serviceProvider, $faceImage, 'face_image', $serviceProvider->getDisk(), true);
        }
    }
}
